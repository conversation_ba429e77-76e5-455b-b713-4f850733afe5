import 'package:flutter/cupertino.dart';

class Dimensions {
  // font-size
  static const double fontOverSmall = 7.00;
  static const double fontExtraSmall = 9.00;
  static const double fontSmall = 11.00;
  static const double fontDefault = 13.00;
  static const double fontLarge = 14.00;
  static const double fontMediumLarge = 17.00;
  static const double fontExtraLarge = 19.00;
  static const double fontOverLarge = 21.00;
  static const double fontMegaLarge = 28.00;

  static const double defaultButtonH = 45;
  static const double defaultRadius = 8;

  static const double space5 = 5;
  static const double space7 = 7;
  static const double space8 = 8;
  static const double space3 = 3;
  static const double space2 = 2;
  static const double space1 = 1;
  static const double space10 = 10;
  static const double space12 = 12;
  static const double space15 = 15;
  static const double space17 = 17;
  static const double space20 = 20;
  static const double space25 = 25;
  static const double space30 = 30;
  static const double space35 = 35;
  static const double space40 = 40;
  static const double space45 = 45;
  static const double space50 = 50;
  static const double space60 = 60;
  static const double space70 = 70;
  static const double space80 = 80;
  static const double space90 = 90;
  static const double space100 = 100;
  static const double space200 = 200;

  static const EdgeInsets screenPaddingHV =
      EdgeInsets.symmetric(horizontal: space15, vertical: space20);
  static const EdgeInsets defaultPaddingHV =
      EdgeInsets.symmetric(vertical: space20, horizontal: space15);

  static const double buttonRadius = 4;
  static const double cardRadius = 8;
  static const double bottomSheetRadius = 15;
  static const double groupCardRadius = 10;
  static const double groupRadius = 20;
  static const double textToTextSpace = 8;
}
