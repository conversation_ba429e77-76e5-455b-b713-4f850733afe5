import 'package:flutex_customer/core/utils/dimensions.dart';
import 'package:flutter/material.dart';

const TextStyle lightOverSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontOverSmall);

const TextStyle lightExtraSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontExtraSmall);

const TextStyle lightSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontSmall);

const TextStyle lightDefault = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontDefault);

const TextStyle lightLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontLarge);

const TextStyle lightMediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontMediumLarge);

const TextStyle lightExtraLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontExtraLarge);

const TextStyle lightOverLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w300,
    fontSize: Dimensions.fontOverLarge);

const TextStyle regularOverSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontOverSmall);

const TextStyle regularExtraSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontExtraSmall);

const TextStyle regularSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontSmall);

const TextStyle dateTextStyle = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontStyle: FontStyle.italic,
    fontSize: Dimensions.fontSmall);

const TextStyle regularDefault = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontDefault);

const TextStyle regularLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontLarge);

const TextStyle regularMediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontMediumLarge);

const TextStyle regularExtraLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontExtraLarge);

const TextStyle regularOverLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontOverLarge);

const TextStyle mediumOverSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontOverSmall);

const TextStyle mediumExtraSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontExtraSmall);

const TextStyle mediumSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontSmall);

const TextStyle mediumDefault = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontDefault);

const TextStyle mediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontLarge);

const TextStyle mediumMediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontMediumLarge);

const TextStyle mediumExtraLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontExtraLarge);

const TextStyle mediumOverLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w500,
    fontSize: Dimensions.fontOverLarge);

const TextStyle semiBoldOverSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontOverSmall);

const TextStyle semiBoldExtraSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontExtraSmall);

const TextStyle semiBoldSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontSmall);

const TextStyle semiBoldDefault = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontDefault);

const TextStyle semiBoldLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontLarge);

const TextStyle semiBoldMediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontMediumLarge);

const TextStyle semiBoldExtraLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontExtraLarge);

const TextStyle semiBoldOverLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontOverLarge);

// semi-bold
const TextStyle boldOverSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontOverSmall);

const TextStyle boldExtraSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontExtraSmall);

const TextStyle boldSmall = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontSmall);

const TextStyle boldDefault = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontDefault);

const TextStyle boldLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontLarge);

const TextStyle boldMediumLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontMediumLarge);

const TextStyle boldExtraLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontExtraLarge);

const TextStyle boldOverLarge = TextStyle(
    fontFamily: 'Montserrat-Arabic',
    fontWeight: FontWeight.w700,
    fontSize: Dimensions.fontOverLarge);
