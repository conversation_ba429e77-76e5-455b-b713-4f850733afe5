import 'dart:convert';
import 'dart:io';

import 'package:flutex_customer/core/utils/local_strings.dart';
import 'package:flutex_customer/common/models/response_model.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutex_customer/core/helper/shared_preference_helper.dart';
import 'package:flutex_customer/core/route/route.dart';
import 'package:flutex_customer/core/utils/method.dart';

class ApiClient extends GetxService {
  SharedPreferences sharedPreferences;
  ApiClient({required this.sharedPreferences});

  Future<ResponseModel> request(
      String uri, String method, Map<String, dynamic>? params,
      {bool passHeader = false}) async {
    Uri url = Uri.parse(uri);
    http.Response response;

    // Create HTTP client with timeout
    final client = http.Client();

    try {
      if (method == Method.postMethod) {
        if (passHeader) {
          initToken();
          response = await client.post(url, body: params, headers: {
            'Accept': 'application/json',
            'Authorization': token,
            'Content-Type': 'application/x-www-form-urlencoded'
          }).timeout(const Duration(seconds: 30));
        } else {
          response = await client.post(url, body: params, headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
          }).timeout(const Duration(seconds: 30));
        }
      } else if (method == Method.putMethod) {
        initToken();
        response = await client.put(url, body: params, headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Accept': 'application/json',
          'Authorization': token
        }).timeout(const Duration(seconds: 30));
      } else if (method == Method.deleteMethod) {
        initToken();
        response = await client.delete(url, headers: {
          'Accept': 'application/json',
          'Authorization': token
        }).timeout(const Duration(seconds: 30));
      } else {
        if (passHeader) {
          initToken();
          response = await client.get(url, headers: {
            'Accept': 'application/json',
            'Authorization': token
          }).timeout(const Duration(seconds: 30));
        } else {
          response = await client.get(url, headers: {
            'Accept': 'application/json'
          }).timeout(const Duration(seconds: 30));
        }
      }

      if (kDebugMode) {
        print('====> url: ${uri.toString()}');
        print('====> method: $method');
        print('====> params: ${params.toString()}');
        print('====> status: ${response.statusCode}');
        print('====> body: ${response.body.toString()}');
        print('====> token: $token');
      }

      StatusModel model = StatusModel.fromJson(jsonDecode(response.body));

      if (response.statusCode == 200) {
        try {
          if (!model.status!) {
            sharedPreferences.setBool(
                SharedPreferenceHelper.rememberMeKey, false);
            sharedPreferences.remove(SharedPreferenceHelper.token);
            Get.offAllNamed(RouteHelper.loginScreen);
          }
        } catch (e) {
          e.toString();
        }

        return ResponseModel(true, model.message!.tr, response.body);
      } else if (response.statusCode == 401) {
        sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
        Get.offAllNamed(RouteHelper.loginScreen);
        return ResponseModel(false, model.message!.tr, response.body);
      } else if (response.statusCode == 404) {
        return ResponseModel(false, model.message!.tr, response.body);
      } else if (response.statusCode == 500) {
        return ResponseModel(
            false, model.message?.tr ?? LocalStrings.serverError.tr, '');
      } else {
        return ResponseModel(
            false, model.message?.tr ?? LocalStrings.somethingWentWrong.tr, '');
      }
    } on SocketException {
      return ResponseModel(false, LocalStrings.somethingWentWrong.tr, '');
    } on FormatException {
      sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
      Get.offAllNamed(RouteHelper.loginScreen);
      return ResponseModel(false, LocalStrings.badResponseMsg.tr, '');
    } on http.ClientException {
      return ResponseModel(false, LocalStrings.somethingWentWrong.tr, '');
    } catch (e) {
      if (e.toString().contains('TimeoutException')) {
        return ResponseModel(
            false, 'Connection timeout. Please try again.', '');
      }
      return ResponseModel(false, e.toString(), '');
    } finally {
      client.close();
    }
  }

  String token = '';

  initToken() {
    if (sharedPreferences.containsKey(SharedPreferenceHelper.accessTokenKey)) {
      String? t =
          sharedPreferences.getString(SharedPreferenceHelper.accessTokenKey);
      token = t ?? '';
    } else {
      token = '';
    }
  }
}
