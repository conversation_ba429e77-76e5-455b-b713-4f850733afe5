import 'package:flutex_customer/common/components/bottom-sheet/bottom_sheet_header_row.dart';
import 'package:flutex_customer/common/components/buttons/rounded_button.dart';
import 'package:flutex_customer/common/components/buttons/rounded_loading_button.dart';
import 'package:flutex_customer/common/components/custom_date_form_field.dart';
import 'package:flutex_customer/common/components/custom_loader/custom_loader.dart';
import 'package:flutex_customer/common/components/text-form-field/custom_drop_down_text_field.dart';
import 'package:flutex_customer/common/components/text-form-field/custom_multi_drop_down_text_field.dart';
import 'package:flutex_customer/common/components/text-form-field/custom_text_field.dart';
import 'package:flutex_customer/core/helper/date_converter.dart';
import 'package:flutex_customer/core/service/api_service.dart';
import 'package:flutex_customer/core/utils/dimensions.dart';
import 'package:flutex_customer/core/utils/local_strings.dart';
import 'package:flutex_customer/core/utils/style.dart';
import 'package:flutex_customer/features/project/controller/project_controller.dart';
import 'package:flutex_customer/features/project/model/task_create_model.dart';
import 'package:flutex_customer/features/project/repo/project_repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class CreateTaskBottomSheet extends StatefulWidget {
  final String projectId;
  const CreateTaskBottomSheet({super.key, required this.projectId});

  @override
  State<CreateTaskBottomSheet> createState() => _CreateTaskBottomSheetState();
}

class _CreateTaskBottomSheetState extends State<CreateTaskBottomSheet> {
  @override
  void initState() {
    Get.put(ApiClient(sharedPreferences: Get.find()));
    Get.put(ProjectRepo(apiClient: Get.find()));
    final controller = Get.put(ProjectController(projectRepo: Get.find()));
    controller.isLoading = true;
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      controller.loadProjectTaskData(widget.projectId);
    });
  }

  @override
  void dispose() {
    Get.find<ProjectController>().clearData();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProjectController>(builder: (controller) {
      return controller.isLoading
          ? const CustomLoader()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BottomSheetHeaderRow(
                  header: LocalStrings.createNewTask.tr,
                  bottomSpace: 0,
                ),
                const SizedBox(height: Dimensions.space20),
                CustomTextField(
                  labelText: LocalStrings.taskTitle.tr,
                  controller: controller.nameController,
                  focusNode: controller.nameFocusNode,
                  textInputType: TextInputType.text,
                  nextFocus: controller.priorityFocusNode,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return LocalStrings.enterTaskTitle.tr;
                    } else {
                      return null;
                    }
                  },
                  onChanged: (value) {
                    return;
                  },
                ),
                const SizedBox(height: Dimensions.space15),
                CustomDropDownTextField(
                  needLabel: true,
                  labelText: LocalStrings.priority.tr,
                  onChanged: (value) {
                    controller.priorityController.text = value;
                  },
                  hintText: LocalStrings.selectPriority.tr,
                  items: controller.taskCreateModel.data!.priority!
                      .map((Priority value) {
                    return DropdownMenuItem(
                      value: value.id,
                      child: Text(
                        value.name?.tr ?? '',
                        style: regularDefault.copyWith(
                            color:
                                Theme.of(context).textTheme.bodyMedium!.color),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: Dimensions.space15),
                CustomMultiDropDownTextField(
                  controller: controller.assigneesController,
                  labelText: LocalStrings.selectAssignees.tr,
                  onChanged: (options) {
                    controller.projectAssignees.clear();
                    for (var v in options) {
                      controller.projectAssignees.add(v.value.toString());
                    }
                    debugPrint(controller.projectAssignees.toString());
                  },
                  items: controller.taskCreateModel.data!.projectMembers!
                      .map((ProjectMembers value) {
                    return DropdownItem(
                        label: '${value.staffFirstName} ${value.staffLastName}',
                        value: value.staffId.toString());
                  }).toList(),
                ),
                const SizedBox(height: Dimensions.space15),
                CustomDateFormField(
                  labelText: LocalStrings.taskStartDate.tr,
                  onChanged: (DateTime? value) {
                    controller.startDateController.text =
                        DateConverter.formatDate(value!);
                  },
                ),
                const SizedBox(height: Dimensions.space15),
                CustomDateFormField(
                  labelText: LocalStrings.taskDueDate.tr,
                  onChanged: (DateTime? value) {
                    controller.dueDateController.text =
                        DateConverter.formatDate(value!);
                  },
                ),
                const SizedBox(height: Dimensions.space15),
                CustomTextField(
                  labelText: LocalStrings.description.tr,
                  textInputType: TextInputType.text,
                  inputAction: TextInputAction.next,
                  focusNode: controller.descriptionFocusNode,
                  controller: controller.descriptionController,
                  onChanged: (value) {
                    return;
                  },
                ),
                const SizedBox(height: Dimensions.space25),
                controller.submitLoading
                    ? const RoundedLoadingBtn()
                    : RoundedButton(
                        text: LocalStrings.submit.tr,
                        press: () {
                          controller.submitTask(widget.projectId);
                        },
                      ),
              ],
            );
    });
  }
}
