name: flutex_customer
description: "Perfex CRM Customer Portal App"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  http: ^1.3.0
  get: ^4.7.2
  lottie: ^3.3.1
  carousel_slider: ^5.0.0
  url_launcher: ^6.3.1
  shared_preferences: ^2.5.2
  share_plus: ^10.1.4
  cached_network_image: ^3.4.1
  connectivity_plus: ^6.1.3
  multi_dropdown: ^3.0.1
  date_field: ^6.0.3+1
  syncfusion_flutter_charts: ^28.2.6
  image_picker: ^1.1.2
  introduction_screen: ^3.1.17
  flutter_widget_from_html: ^0.16.0
  flutter_html: ^3.0.0-beta.2
  flutter_spinkit: ^5.2.1
  contained_tab_bar_view: ^0.8.0
  file_picker: ^9.0.0
  html: ^0.15.4
  network_info_plus: ^6.1.3
  signature: ^6.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^5.0.0

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section
  assets:
    - assets/images/
    - assets/animation/
    - assets/lang/

  fonts:
    - family: Montserrat-Arabic
      fonts:
        - asset: assets/fonts/Montserrat-Arabic-Light.ttf
          weight: 300
        - asset: assets/fonts/Montserrat-Arabic-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat-Arabic-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat-Arabic-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat-Arabic-Bold.ttf
          weight: 700
