                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\Android\Sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\Android\Sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\Android\Sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\Flutex\flutex_customer\build\app\intermediates\cxx\Debug\1z4j1g5g\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\Flutex\flutex_customer\build\app\intermediates\cxx\Debug\1z4j1g5g\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BF:\Flutex\flutex_customer\android\app\.cxx\Debug\1z4j1g5g\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2