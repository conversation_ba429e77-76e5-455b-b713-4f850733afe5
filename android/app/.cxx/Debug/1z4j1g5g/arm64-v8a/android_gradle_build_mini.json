{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\Flutex\\flutex_customer\\android\\app\\.cxx\\Debug\\1z4j1g5g\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\Flutex\\flutex_customer\\android\\app\\.cxx\\Debug\\1z4j1g5g\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}