<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">flutex.branditta.net</domain>
        <trust-anchors>
            <!-- Trust preinstalled CAs -->
            <certificates src="system"/>
            <!-- Additionally trust user added CAs -->
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
    
    <!-- Allow cleartext traffic for debugging purposes only -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
</network-security-config>
